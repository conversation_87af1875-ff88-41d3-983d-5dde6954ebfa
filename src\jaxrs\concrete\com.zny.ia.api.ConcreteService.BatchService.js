/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const BatchService = {
    /**
     * 结束种植批次
     * @param {*} batchId 种植批次号
     * @returns Promise 
     */
    'closedBatch': function (batchId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a338f672ec313d3483adb3973c1c27a35d889554', batchId)
            : execute(concreteModuleName, `/BatchService/closedBatch`, 'json', 'POST', { batchId });
    }, 
    /**
     * 批次历史记录
     * @param {*} pageRequest 查询条件
     * @returns Promise 
     */
    'batchHistoryListInformation': function (pageRequest) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a6d8cdab00dce09aec0cda952d113a45039e8b0c', pageRequest)
            : execute(concreteModuleName, `/BatchService/batchHistoryListInformation`, 'json', 'POST', pageRequest);
    }, 
    /**
     * 根据作物获取批次信息
     * @param {*} crop 作物
     * @returns Promise 
     */
    'findBatchListByCrop': function (crop) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'f904c40c2798716619d3da6ba590038a8eef0cc3', crop)
            : execute(concreteModuleName, `/BatchService/findBatchListByCrop`, 'json', 'POST', { crop });
    }, 
    /**
     * 总览批次信息
     * @param {*} pageRequest 查询条件
     * @returns Promise 
     */
    'batchOverviewListInformation': function (pageRequest) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '7f3145eaa2152bc4329982adf3152ab761c6157b', pageRequest)
            : execute(concreteModuleName, `/BatchService/batchOverviewListInformation`, 'json', 'POST', pageRequest);
    }, 
    /**
     * 填报收获批次产量
     * @param {*} harvestId 收获批次号
     * @param {*} production 产量
     * @returns Promise 
     */
    'fillInProduction': function (harvestId, production) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '897f042bed6396f5de29da7ff1bdebe419c39e86', {harvestId, production})
            : execute(concreteModuleName, `/BatchService/fillInProduction`, 'json', 'POST', { harvestId, production });
    }, 
    /**
     * 生成收获批次
     * @param {*} harvestBatchPo 收获批次信息
     * @returns Promise 
     */
    'generateHarvestBatch': function (harvestBatchPo) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'b977b53bfa9b15f1b0469080c1709a5cd3133ef4', harvestBatchPo)
            : execute(concreteModuleName, `/BatchService/generateHarvestBatch`, 'text', 'POST', harvestBatchPo);
    }, 
    /**
     * 种植批次是否有收获批次
     * @param {*} batchId 种植批次号
     * @returns Promise 
     */
    'checkHarvestBatch': function (batchId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '90468ac64a69a2fe6d70b74505ec74e4b4c26587', batchId)
            : execute(concreteModuleName, `/BatchService/checkHarvestBatch`, 'json', 'POST', { batchId });
    }, 
    /**
     * 生成种植批次
     * @param {*} generateBatchPo 生成种植批次信息
     * @returns Promise 
     */
    'generateBatch': function (generateBatchPo) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'dfcd1a10b504da6475dfad07132d93f712823936', generateBatchPo)
            : execute(concreteModuleName, `/BatchService/generateBatch`, 'text', 'POST', generateBatchPo);
    }, 
    /**
     * 更新批次作物生长阶段
     * @param {*} batchIds 批次号
     * @param {*} growStage 作物生长期
     * @returns Promise 
     */
    'updateGrowStage': function (batchIds, growStage) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'b5b40c13d4ffc8344eb8667a367fa084ac38de26', {batchIds, growStage})
            : execute(concreteModuleName, `/BatchService/growStage`, 'json', 'PUT', { batchIds, growStage });
    }, 
    /**
     * 生成种植批次时能够选择的区域
     * @returns Promise 
     */
    'generateBatchChooseArea': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '5687b2d2bd5576027f7a9e59eed055b3f05f5a6b')
            : execute(concreteModuleName, `/BatchService/generateBatchChooseArea`, 'json', 'GET');
    }, 
    /**
     * 区域批次信息
     * @param {*} pageRequest 查询条件
     * @returns Promise 
     */
    'batchAreaListInformation': function (pageRequest) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '196772a53526ae9f0b9a9c1958f0a268ed54e2e5', pageRequest)
            : execute(concreteModuleName, `/BatchService/batchAreaListInformation`, 'json', 'POST', pageRequest);
    }, 
    /**
     * 批次详情
     * @param {*} batchId 种植批次号
     * @returns Promise 
     */
    'batchInformationByBatchId': function (batchId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'af4182b03420678f0d50924bb5b07eb3e2dce286', batchId)
            : execute(concreteModuleName, `/BatchService/batchInformationByBatchId`, 'json', 'POST', { batchId });
    }, 
    /**
     * 填报收获批次产值
     * @param {*} harvestId 收获批次号
     * @param {*} outputValue 产值
     * @returns Promise 
     */
    'fillInOutputValue': function (harvestId, outputValue) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a8cba8585c97261fecdcc8d9dfed4ff59e940b87', {harvestId, outputValue})
            : execute(concreteModuleName, `/BatchService/fillInOutputValue`, 'json', 'POST', { harvestId, outputValue });
    }, 
    /**
     * 生成结束种植批次-根据作物查询种植该作物的区域以及对应种植批次号
     * @param {*} crop 作物类型
     * @returns Promise 
     */
    'areaAndBatchByCorp': function (crop) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '3756840c5a604d22574a08bc1c798d0ce3eefc10', crop)
            : execute(concreteModuleName, `/BatchService/areaAndBatchByCorp`, 'json', 'POST', { crop });
    }
}

export default BatchService
